// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_media_editor/v_chat_media_editor.dart';
import 'package:v_platform/v_platform.dart';

import '../../../../../core/api_service/story/story_api_service.dart';
import '../../../../../core/models/story/story_model.dart';
import '../../../../../core/utils/permission_manager.dart';
import '../../../../story/media_story/create_media_story.dart';
import '../../../../story/text_story/create_text_story.dart';

class StoryTabState {
  List<UserStoryModel> allStories = [];
  List<UserStoryModel> viewedStories = [];
  UserStoryModel myStories =
      UserStoryModel(stories: [], userData: AppAuth.myProfile.baseUser);
  bool isMyStoriesLoading = false;

  // Helper methods to separate viewed and unviewed stories
  List<UserStoryModel> get unviewedStories {
    return allStories.where((userStory) {
      // Show in recent updates if ANY story is unviewed
      return userStory.stories.any((story) => !story.viewedByMe);
    }).toList();
  }

  List<UserStoryModel> get completelyViewedStories {
    return allStories.where((userStory) {
      // Show in viewed updates if ALL stories are viewed
      return userStory.stories.isNotEmpty &&
          userStory.stories.every((story) => story.viewedByMe);
    }).toList();
  }
}

class StoryTabController extends SLoadingController<StoryTabState> {
  StoryTabController() : super(SLoadingState(StoryTabState()));
  final _apiService = GetIt.I.get<StoryApiService>();
  Timer? _timer;
  final Set<String> _processingStoryIds = <String>{};

  @override
  void onInit() {
    getStories();
    _timer = Timer.periodic(const Duration(seconds: 30), (timer) {
      getStoriesFromApi();
    });
    getMyStoryFromApi();
  }

  @override
  void onClose() {
    _timer?.cancel();
    _processingStoryIds.clear();
  }

  void getStories() async {
    try {
      final oldStories = VAppPref.getMap("api/stories/all");
      if (oldStories != null) {
        final list = oldStories['data'] as List;
        data.allStories = list.map((e) => UserStoryModel.fromMap(e)).toList();
        setStateSuccess();
      }
    } catch (err) {
      if (kDebugMode) {
        print(err);
      }
    }
    await getStoriesFromApi();
  }

  Future<void> getStoriesFromApi() async {
    vSafeApiCall(
      request: () {
        return _apiService.getUsersStories(page: 1, limit: 50);
      },
      onSuccess: (response) {
        for (int i = response.length - 1; i >= 0; i--) {
          var item = response[i];
          if (!data.allStories.contains(item)) {
            data.allStories.insert(0, item);
          }
        }
        if (response.isEmpty) {
          data.allStories.clear();
          unawaited(VAppPref.removeKey("api/stories/all"));
        } else {
          unawaited(VAppPref.setMap("api/stories/all", {
            "data": response.map((e) => e.toMap()).toList(),
          }));
        }
        setStateSuccess();
        update();
      },
    );
  }

  Future getMyStoryFromApi() async {
    vSafeApiCall<UserStoryModel?>(
      request: () {
        return _apiService.getMyStories();
      },
      onSuccess: (response) {
        if (response == null) return;
        data.myStories = response;
        setStateSuccess();
        update();
      },
    );
  }

  void toCreateStory(BuildContext context) async {
    final res = await VAppAlert.showModalSheetWithActions(
      content: [
        ModelSheetItem(
          title: S.of(context).createTextStory,
          id: "1",
        ),
        ModelSheetItem(
          title: S.of(context).createMediaStory,
          id: "2",
        ),
      ],
      context: context,
    );
    if (res == null) return;
    if (res.id == "1") {
      await context.toPage(
        const CreateTextStory(),
      );
    }
    if (res.id == "2") {
      final pickOption = await _processPickMedia(context);
      if (pickOption == null)
        return; // User canceled initial camera/gallery choice
      await _handleMediaStoryCreation(context, pickOption);
    }
    getMyStoryFromApi();
  }

  Future<void> _handleMediaStoryCreation(
      BuildContext context, int pickOption) async {
    while (true) {
      // Loop to allow re-picking media
      VPlatformFile? mediaFile;
      if (pickOption == 1) {
        mediaFile = await _onCameraPress(context);
      } else {
        mediaFile = await _pickFromGallery(context);
      }

      if (mediaFile == null) {
        // User canceled media picking (from camera or gallery)
        return; // Exit the loop and the function
      }

      final mediaAfterEdit = await onSubmitMedia(context, [mediaFile]);
      if (mediaAfterEdit == null) {
        // User canceled from the editor. Loop will continue to re-prompt for media picking.
        continue;
      }

      // If we reach here, mediaAfterEdit is not null, so we proceed to CreateMediaStory
      await context.toPage(
        CreateMediaStory(
          media: mediaAfterEdit,
        ),
      );
      return; // Exit the loop and the function after successful story creation
    }
  }

  Future<VPlatformFile?> _onCameraPress(BuildContext context) async {
    final isCameraAllowed = await PermissionManager.isCameraAllowed();
    if (!isCameraAllowed) {
      final x = await PermissionManager.askForCamera();
      if (!x) return null;
    }
    final entity = await VAppPick.pickFromWeAssetCamera(
      context: context,
    );
    if (entity == null) return null;
    return entity;
  }

  Future<VPlatformFile?> _pickFromGallery(BuildContext context) async {
    try {
      // Use FilePicker to allow both images and videos
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.media, // This allows both images and videos
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = result.files.single;
        // Convert PlatformFile to VPlatformFile
        return VPlatformFile.fromPath(
          fileLocalPath: file.path!,
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error picking file from gallery: $e');
      }
      // Fallback to the original image picker if FilePicker fails
      return await VAppPick.getImage(isFromCamera: false);
    }
  }

  Future<VBaseMediaRes?> onSubmitMedia(
    BuildContext context,
    List<VPlatformFile> files,
  ) async {
    final fileRes = await context.toPage(VMediaEditorView(
      files: files,
    )) as List<VBaseMediaRes>?;
    if (fileRes == null || fileRes.isEmpty) return null;
    return fileRes.first;
  }

  Future<int?> _processPickMedia(BuildContext context) async {
    final res = await VAppAlert.showModalSheetWithActions(
      content: [
        ModelSheetItem(
          title: S.of(context).camera,
          id: "1",
        ),
        ModelSheetItem(
          title: S.of(context).gallery,
          id: "2",
        ),
      ],
      context: context,
    );
    if (res == null) return null;
    return res.id == "1" ? 1 : 2;
  }

  // Method to mark a story as viewed
  void markStoryAsViewed(String storyId) {
    // Use delayed call to avoid setState during build
    Future.delayed(Duration.zero, () {
      _updateStoryViewStatus(storyId);
    });
  }

  void _updateStoryViewStatus(String storyId) {
    // Prevent duplicate processing
    if (_processingStoryIds.contains(storyId)) return;
    _processingStoryIds.add(storyId);

    try {
      for (int i = 0; i < data.allStories.length; i++) {
        final userStory = data.allStories[i];
        for (int j = 0; j < userStory.stories.length; j++) {
          if (userStory.stories[j].id == storyId) {
            // Skip if already viewed
            if (userStory.stories[j].viewedByMe) {
              _processingStoryIds.remove(storyId);
              return;
            }

            // Create a new story with viewedByMe = true
            final updatedStory = StoryModel(
              id: userStory.stories[j].id,
              userId: userStory.stories[j].userId,
              content: userStory.stories[j].content,
              backgroundColor: userStory.stories[j].backgroundColor,
              caption: userStory.stories[j].caption,
              att: userStory.stories[j].att,
              expireAt: userStory.stories[j].expireAt,
              createdAt: userStory.stories[j].createdAt,
              updatedAt: userStory.stories[j].updatedAt,
              storyType: userStory.stories[j].storyType,
              fontType: userStory.stories[j].fontType,
              viewedByMe: true,
            );

            // Create a new list with the updated story
            final updatedStories = List<StoryModel>.from(userStory.stories);
            updatedStories[j] = updatedStory;

            // Create a new UserStoryModel with updated stories
            final updatedUserStory = UserStoryModel(
              userData: userStory.userData,
              stories: updatedStories,
            );

            // Update the allStories list
            data.allStories[i] = updatedUserStory;

            // Trigger UI update
            setStateSuccess();
            update();
            _processingStoryIds.remove(storyId);
            return;
          }
        }
      }
    } finally {
      _processingStoryIds.remove(storyId);
    }
  }
}
